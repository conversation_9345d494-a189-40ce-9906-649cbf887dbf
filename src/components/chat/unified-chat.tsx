"use client";
import React, { Suspense, useContext, useEffect, useState } from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import {
  ChatContainer,
  ConversationHeader,
  Message,
  MessageList,
  Avatar,
  MessageInput,
} from "@chatscope/chat-ui-kit-react";
import { CheckCircle } from "lucide-react";
import FileDisplayContainer from "@/components/fileDisplayContainer";
import { Loader } from "../shared/loader";
import { SocketContext } from "@/context/socket";
import useNotification from "@/hooks/useNotification";
import { useChat } from "@/hooks/useChat";
import { ChatComponentProps } from "@/types/chat";
import LogoutButton from "@/components/auth/logout-button";
import {
  formatUserName,
  sanitizeHtml,
  isImageType,
  validateFile,
} from "@/lib/utils";
import { toast } from "sonner";

/**
 * Unified Chat Component
 * Consolidates duplicate chat functionality from multiple components
 * Supports both patient and provider roles
 */
const UnifiedChatComponent: React.FC<ChatComponentProps> = ({
  patient,
  provider,
  room,
  role,
}) => {
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [imageModalValue, setImageModalValue] = useState("");
  const { isConnected } = useContext(SocketContext);
  const sendNotification = useNotification();

  // Determine sender and receiver based on role
  const isProvider = role === "PROVIDER";
  const currentUser = isProvider ? provider : patient;
  const otherUser = isProvider ? patient : provider;
  const senderId = currentUser.user_id;
  const roomId = room.id.toString();

  // Use the chat hook for all chat functionality
  const {
    messageHistory,
    sendMessage,
    uploadFile,
    getFileSignUrl,
    loadMessageHistory,
    markMessagesAsRead,
    isLoading,
    error,
  } = useChat(roomId, senderId);

  // Load message history on component mount
  useEffect(() => {
    loadMessageHistory();
    markMessagesAsRead();
  }, [loadMessageHistory, markMessagesAsRead]);

  // Handle incoming messages for notifications
  useEffect(() => {
    if (messageHistory.length > 0) {
      const lastMessage = messageHistory[messageHistory.length - 1];
      if (lastMessage.sender.user_id !== senderId) {
        sendNotification({
          title: `New message from ${formatUserName(
            lastMessage.sender.first_name,
            lastMessage.sender.last_name,
          )}`,
          body:
            lastMessage.message === "ATTACHMENT"
              ? "Sent a file"
              : lastMessage.message,
        });
      }
    }
  }, [messageHistory, senderId, sendNotification]);

  const handleMessageSend = async (message: string) => {
    await sendMessage(message);
  };

  const handleFileUpload = async (e: Event) => {
    const target = e.target as HTMLInputElement;
    if (!target.files || target.files.length === 0) return;

    const file = target.files[0];
    const validation = validateFile(file);

    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    await uploadFile(file);
    // Reset the input
    target.value = "";
  };

  const handleFileClick = async (fileId: string) => {
    const url = await getFileSignUrl(fileId);
    if (url) {
      setImageModalValue(url);
      setImageModalVisible(true);
    }
  };

  const handleOnAttached = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".jpg,.jpeg,.png,.pdf,.doc,.docx,.heic";
    input.onchange = handleFileUpload;
    input.click();
  };

  // Format display names
  const receiverFullName = formatUserName(
    otherUser.first_name,
    otherUser.last_name,
  );

  return (
    <Suspense fallback={<Loader show={true} />}>
      <Loader show={isLoading} />

      {/* File Preview Modal */}
      <FileDisplayContainer
        imageModalVisible={imageModalVisible}
        setImageModalVisible={setImageModalVisible}
        src={imageModalValue}
      />

      <ChatContainer style={{ height: "100vh" }}>
        <ConversationHeader>
          <Avatar name={receiverFullName} src="/images/avatar.png" />
          <ConversationHeader.Content userName={receiverFullName} />
          <ConversationHeader.Actions className="flex items-center gap-2">
            {isConnected && <CheckCircle className="text-green-500" />}
            <LogoutButton variant="ghost" size="icon" showText={false} />
          </ConversationHeader.Actions>
        </ConversationHeader>

        <MessageList>
          {messageHistory.map((message, idx) => {
            const isSenderByMe = message.sender?.user_id === senderId;
            const messageContent =
              message.message === "ATTACHMENT"
                ? "Sent a file"
                : sanitizeHtml(message.message);

            return (
              <Message
                key={`${message.id || idx}-${message.created_at || Date.now()}`}
                onClick={() => {
                  if (message.file?.path) {
                    handleFileClick(message.file.path);
                  }
                }}
                model={{
                  direction: isSenderByMe ? "outgoing" : "incoming",
                  message: messageContent,
                  position: "last",
                  sender: formatUserName(
                    message.sender.first_name,
                    message.sender.last_name,
                  ),
                  sentTime: message.created_at
                    ? new Date(message.created_at).toLocaleTimeString()
                    : "now",
                }}
              >
                {message.message === "ATTACHMENT" && message.file ? (
                  <Message.ImageContent
                    className="cursor-pointer"
                    src={`/images/${
                      isImageType(message.file.path) ? "file.png" : "pdf.png"
                    }`}
                    height={100}
                    alt="Attachment"
                  />
                ) : (
                  <Message.HtmlContent html={messageContent} />
                )}

                {!isSenderByMe && <Avatar name="" src="/images/avatar.png" />}
              </Message>
            );
          })}
        </MessageList>

        <MessageInput
          autoFocus
          placeholder="Type message here"
          onSend={handleMessageSend}
          onAttachClick={handleOnAttached}
          disabled={isLoading}
        />
      </ChatContainer>

      {error && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white p-3 rounded-md">
          {error}
        </div>
      )}
    </Suspense>
  );
};

export default UnifiedChatComponent;
